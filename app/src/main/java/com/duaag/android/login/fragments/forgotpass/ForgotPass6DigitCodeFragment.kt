package com.duaag.android.login.fragments.forgotpass

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import androidx.fragment.app.Fragment
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.amazonaws.services.cognitoidentityprovider.model.CodeMismatchException
import com.amazonaws.services.cognitoidentityprovider.model.LimitExceededException
import com.amazonaws.services.cognitoidentityprovider.model.UserLambdaValidationException
import com.duaag.android.R
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.broadcasts.SMSReceiver
import com.duaag.android.clevertap.ClevertapEventEnum
import com.duaag.android.clevertap.ClevertapEventPropertyEnum
import com.duaag.android.clevertap.ClevertapSignInSourceValues
import com.duaag.android.clevertap.sendClevertapEvent
import com.duaag.android.clevertap.sendLogInEventCleverTapUxCam
import com.duaag.android.databinding.FragmentVerifyCodeBinding
import com.duaag.android.exceptions.UserNotExistException
import com.duaag.android.launcher.SplashActivity
import com.duaag.android.logevents.firebaseanalytics.FirebaseAnalyticsEventsName
import com.duaag.android.logevents.firebaseanalytics.firebaseLogEvent
import com.duaag.android.logevents.firebaseanalytics.logSignUpEvent
import com.duaag.android.login.StartActivity
import com.duaag.android.login.viewmodels.SignInViewModel
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.models.ForgotPasswordAuthResult
import com.duaag.android.utils.*
import com.duaag.android.uxcam.sendUxCamEvent
import com.duaag.android.views.PinField
import com.google.android.gms.auth.api.phone.SmsRetriever


import javax.inject.Inject


class ForgotPass6DigitCodeFragment : Fragment() {

    @Inject
    lateinit var viewModelFactory: ViewModelProvider.Factory
    private val signInViewModel by viewModels<SignInViewModel>({ activity as StartActivity }) { viewModelFactory }

    private var _binding: FragmentVerifyCodeBinding? = null
    private val binding get() = _binding!!

    private val deepLinkReceiver = ConfirmCodeBroadcastReceiver()

    private var smsIntentFilter: IntentFilter? = null
    private var smsReceiver: SMSReceiver? = null


    override fun onAttach(context: Context) {
        super.onAttach(context)

        updateLocale(context)

        (requireActivity() as StartActivity).loginComponent.inject(this)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val start = getString(R.string.we_ve_sent_a_6_digit_code)
        val email = " ${signInViewModel.phoneEmail.value?.lowercase()}"
        val end = getString(R.string.please_enter_your)
        val wordtoSpan: Spannable = SpannableString(start + email + end)

        // Set foundation_description_primary color for the first part
        wordtoSpan.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.foundation_description_primary)),
            0,
            start.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        // Set title_secondary color for the second part (email/phone)
        wordtoSpan.setSpan(
            ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.foundation_title_primary)),
            start.length,
            start.length + email.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )

        binding.emailDescription.text = wordtoSpan
        //Assign custom listener to our custom pin view
        binding.modifiedPinView.onTextCompleteListener = PinFiledListener()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        // Inflate the layout for this fragment
        _binding = FragmentVerifyCodeBinding.inflate(inflater, container, false)

        send6digitCodeSignInEvent()

        // Set soft input mode to adjust resize to prevent keyboard from covering content
        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            requireActivity().registerReceiver(deepLinkReceiver, IntentFilter(SplashActivity.CONFIRMATION_CODE_INTENT_FILTER), Context.RECEIVER_NOT_EXPORTED)
        } else {
            requireActivity().registerReceiver(deepLinkReceiver, IntentFilter(SplashActivity.CONFIRMATION_CODE_INTENT_FILTER))
        }
        initSmsListener()
        initSmsBroadCast()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            requireActivity().registerReceiver(smsReceiver,smsIntentFilter, Context.RECEIVER_NOT_EXPORTED)
        } else {
            requireActivity().registerReceiver(smsReceiver,smsIntentFilter)
        }

        binding.resendBtn.setOnSingleClickListener(1000L) {
            signInViewModel.setTimer()
            (requireActivity() as StartActivity).executeForgotPassReCaptcha()
        }


        signInViewModel.forgotPassResultLiveData.observe(viewLifecycleOwner) {
            when (it) {
                is ForgotPasswordAuthResult.Loading -> {
                }

                is ForgotPasswordAuthResult.Error -> {
                    when (it.e) {
                        is CodeMismatchException -> {
                            //set error UI wrong code instead of showing dialog
                            binding.errorText.visibility = View.VISIBLE
                            binding.errorText.text = getString(R.string.onboarding_code_error)
                            setBorderColorPinView(R.color.fields_required)
                        }

                        is UserLambdaValidationException -> {
                            when {
                                it.e.message?.contains("token_not_accepted") == true -> {
                                    errorDialog(getString(R.string.an_error_occurred))
                                    blockedDialog()
                                }

                                it.e.message?.contains("CustomMessage") == true -> {
                                    val message = getHourFromErrorMessage(it.e.errorMessage)
                                    errorDialog(message)
                                }
                                else -> errorDialog(getString(R.string.an_error_occurred))
                            }

                        }

                        is LimitExceededException -> {
                            errorDialog(getString(R.string.attempt_limit_reached_try_again_later))
                        }

                        else -> {
                            errorDialog(getString(R.string.an_error_occurred))
                        }
                    }
                    logError(ErrorStatus.FORGOT_PASS_RESULT_LIVE_DATA)
                }
                is ForgotPasswordAuthResult.CustomSmsVerification ->
                    signInViewModel.onCustomSmsVerificationUsed(true)
                else -> {}
            }

        }


        signInViewModel.elapsedTime.observe(viewLifecycleOwner) {
            val string = getString(R.string.you_can_resend_the_code_an, it)
            binding.timerTextV.text = string
        }

        signInViewModel.showTimer.observe(viewLifecycleOwner) {
            binding.resendBtn.isVisible = !it
            binding.timerTextV.isVisible = it
        }

        signInViewModel.verifyDigits.observe(viewLifecycleOwner){
            it?.let {
                binding.modifiedPinView.setText(it)
            }
        }

        signInViewModel.doNotShowTimmerIfLimitIsReached.observe(viewLifecycleOwner){
            binding.timerTextV.visibility = View.GONE
            binding.resendBtn.visibility = View.VISIBLE
            signInViewModel.stopTimer()
        }


        signInViewModel.forgotPassCustomSmsResult.observe(viewLifecycleOwner) {
            when (it) {
                is ForgotPasswordAuthResult.Loading -> {
                    binding.modifiedPinView.apply {
                        isEnabled = false
                    }
                }
                is ForgotPasswordAuthResult.Error -> {
                    binding.modifiedPinView.apply {
                        isEnabled = true
                        showKeyboard()
                    }
                    when (it.e) {
                        is CodeMismatchException -> {
                            //set error UI wrong code instead of showing dialog
                            binding.errorText.visibility = View.VISIBLE
                            binding.errorText.text = getString(R.string.onboarding_code_error)
                            setBorderColorPinView(R.color.fields_required)
                        }
                        is LimitExceededException -> {
                            errorDialog(getString(R.string.attempt_limit_reached_try_again_later))
                        }
                        is UserNotExistException -> {
                            errorDialog(getString(R.string.user_does_not_exist))
                        }
                        else -> {
                            errorDialog(getString(R.string.smthg_went_wrong))
                            logError(ErrorStatus.FORGOT_PASSWORD_SMS)
                        }
                    }
                }
                else -> {}
            }
        }

        lifecycleScope.launchWhenResumed {
            binding.modifiedPinView.requestFocus()
            binding.modifiedPinView.showKeyboard()
        }


        return binding.root
    }

    @SuppressLint("InflateParams")
    private fun errorDialog(errorMessage:String) {
        val builder = AlertDialog.Builder(requireContext(), R.style.ThemeOverlay_MaterialComponents_Dialog_Alert)

        builder.apply {
            @Suppress("UNUSED_ANONYMOUS_PARAMETER")
            setTitle("oopps!")
            setMessage(errorMessage)
                .setNegativeButton(getString(R.string.ok_dialog)) { dialog, which ->
                    dialog.cancel()
                }
        }
        return builder.create().run {
            setOnShowListener {
                getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(context, R.color.blue_500))
            }
            show()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        requireActivity().unregisterReceiver(deepLinkReceiver)
        requireActivity().unregisterReceiver(smsReceiver)
        smsReceiver = null
        smsIntentFilter = null
        _binding = null
    }

    @SuppressLint("InflateParams")
    private fun blockedDialog() {
        firebaseLogEvent(FirebaseAnalyticsEventsName.RECAPTCHA_BLOCKED_USER)

        val builder = AlertDialog.Builder(requireContext(), R.style.ThemeOverlay_MaterialComponents_Dialog_Alert)

        builder.apply {
            @Suppress("UNUSED_ANONYMOUS_PARAMETER")
            setMessage(getString(R.string.blocked_suspicious_activity))
                .setNegativeButton(getString(R.string.ok_dialog)) { dialog, which ->
                    dialog.cancel()
                }
        }
        return builder.create().run {
            setOnShowListener {
                getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(ContextCompat.getColor(context, R.color.blue_500))
            }
            show()
        }
    }

    private fun initSmsBroadCast() {
        smsIntentFilter = IntentFilter(SmsRetriever.SMS_RETRIEVED_ACTION)
        smsReceiver = SMSReceiver()
        smsReceiver?.setOTPListener(object : SMSReceiver.OTPReceiveListener {
            override fun onOTPReceived(otp: String?) {
                if(!otp.isNullOrEmpty() && otp.length == 6)
                    binding.modifiedPinView.setText(otp)
            }
        })
    }

    private fun initSmsListener() {
        val client = SmsRetriever.getClient(requireActivity())
        client.startSmsRetriever()
    }

    private fun setBorderColorPinView(color: Int) {
        binding.modifiedPinView.fieldColor = ContextCompat.getColor(requireContext(), color)
    }

    private fun send6digitCodeSignInEvent() {
        sendClevertapEvent(ClevertapEventEnum.SIX_DIGIT_CODE_SIGN_IN,
            mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signInViewModel.authMethod.value),
            false
        )
        sendUxCamEvent(ClevertapEventEnum.SIX_DIGIT_CODE_SIGN_IN,
            mapOf(ClevertapEventPropertyEnum.SIGN_UP_OR_SIGN_IN_MEDIUM.propertyName to signInViewModel.authMethod.value)
        )
    }

    inner class PinFiledListener : PinField.OnTextCompleteListener {
        override fun onTextComplete(enteredText: String): Boolean {
            logSignUpEvent(signInViewModel.authMethod.value, FirebaseAnalyticsEventsName.FP_6DIGIT_CODE)

            signInViewModel.verifyForgotPassword(enteredText)
            return false
        }

        override fun onTextChange(enteredText: String) {
            binding.errorText.visibility = View.GONE
            setBorderColorPinView(R.color.bg_input)
        }
    }

    inner class ConfirmCodeBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val code = intent?.getStringExtra(SplashActivity.DEEP_LINK_CONFIRMATION_CODE)
            if(!code.isNullOrEmpty() && code.length == 6)
                binding.modifiedPinView.setText(code)
        }
    }



}