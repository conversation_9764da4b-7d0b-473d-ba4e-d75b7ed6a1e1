package com.duaag.android.login.viewmodels

import android.os.CountDownTimer
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.amazonaws.mobile.client.results.ForgotPasswordResult
import com.amazonaws.mobile.client.results.ForgotPasswordState
import com.amazonaws.mobile.client.results.SignInResult
import com.amazonaws.services.cognitoidentityprovider.model.CodeMismatchException
import com.amazonaws.services.cognitoidentityprovider.model.LimitExceededException
import com.amazonaws.services.cognitoidentityprovider.model.UserLambdaValidationException
import com.applovin.sdk.AppLovinSdk
import com.duaag.android.R
import com.duaag.android.api.Resource
import com.duaag.android.api.ResourceV2
import com.duaag.android.api.Result
import com.duaag.android.application.DuaApplication
import com.duaag.android.appsflyer.domain.AppsFlyerBackendManager
import com.duaag.android.auth_interfaces.AuthMethodChooser
import com.duaag.android.auth_interfaces.HasConnectWithThirdParty
import com.duaag.android.aws.AWSInteractor
import com.duaag.android.aws.ForgotPasswordCallBack
import com.duaag.android.aws.LogInCallBack
import com.duaag.android.aws.models.ConfirmForgotPasswordModel
import com.duaag.android.aws.models.ForgotPasswordModel
import com.duaag.android.aws.models.LoginModel
import com.duaag.android.aws.models.VerifyCustomSmsModel
import com.duaag.android.base.error_logs.ErrorLogManager.logError
import com.duaag.android.base.error_logs.ErrorStatus
import com.duaag.android.clevertap.linkCleverTapAppsFlyer
import com.duaag.android.last_login.domain.use_case.GetLastLoggedInUseCase
import com.duaag.android.di.ActivityScope
import com.duaag.android.exceptions.ServerErrorException
import com.duaag.android.exceptions.UserDeletedExistException
import com.duaag.android.exceptions.UserNotExistException
import com.duaag.android.fingerprint_pro.domain.usecase.VisitorIdUseCase
import com.duaag.android.firebase.NotificationRepository
import com.duaag.android.last_login.domain.model.LastLoggedIn
import com.duaag.android.logevents.firebaseanalytics.setUserId
import com.duaag.android.login.models.*
import com.duaag.android.sharedprefs.DuaSharedPrefs
import com.duaag.android.signup.models.AuthMethod
import com.duaag.android.signup.models.ForgotPasswordAuthResult
import com.duaag.android.signup.models.ForgotPasswordAuthResult.Loading
import com.duaag.android.signup.models.SignInAuthResult
import com.duaag.android.user.DuaAccount
import com.duaag.android.user.UserRepository
import com.duaag.android.utils.ToastUtil
import com.duaag.android.utils.livedata.SingleLiveData
import com.facebook.login.LoginManager
import com.google.android.libraries.identity.googleid.GetGoogleIdOption
import com.uxcam.UXCam
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

@ActivityScope
class SignInViewModel @Inject constructor(
    val userRepository: UserRepository,
    val notificationRepository: NotificationRepository,
    private val appsFlyerBackendManager: AppsFlyerBackendManager,
    private var visitorIdUseCase: VisitorIdUseCase,
    private var getLastLoggedInUseCase: GetLastLoggedInUseCase,
    val duaSharedPrefs: DuaSharedPrefs
) : ViewModel(), AuthMethodChooser, HasConnectWithThirdParty {

    companion object {
        const val TAG = "SignInViewModel"
    }

    @Inject
    lateinit var duaAccount: DuaAccount

    var phoneNumber: String = ""
        private set

    var userPassword: String = ""
        private set

    var userEmail: String = ""
        private set

    var phoneEmail = MutableLiveData<String>()

    var selectedCountryCodeAsInt: Int? = null
    var thirdPartyUserData: ThirdPartyUserData? = null
    var loggingInProgress = false
    var signUpWithSpottedEntity: SignUpWithSpottedEntity? = null
    var signUpWithSpottedInProgress: Boolean = false

    private val _signInResultLiveData = MutableLiveData<SignInAuthResult>()
    val signInResultLiveData: LiveData<SignInAuthResult>
        get() = _signInResultLiveData

    private val _authMethod: SingleLiveData<AuthMethod> = SingleLiveData()
    val authMethod: LiveData<AuthMethod>
        get() = _authMethod

    private var _userNotFound: SingleLiveData<Boolean> = SingleLiveData()
    val userNotFound: LiveData<Boolean>
        get() = _userNotFound

    private val _onForgotPasswordClicked: SingleLiveData<AuthMethod> = SingleLiveData()
    val onForgotPasswordClicked: LiveData<AuthMethod>
        get() = _onForgotPasswordClicked

    private val _showHidenFaceBookProgressBar: SingleLiveData<Boolean> = SingleLiveData()
    val showHidenFaceBookProgressBar: LiveData<Boolean>
        get() = _showHidenFaceBookProgressBar

    private val _forgotPassResultLiveData = SingleLiveData<ForgotPasswordAuthResult>()
    val forgotPassResultLiveData: LiveData<ForgotPasswordAuthResult>
        get() = _forgotPassResultLiveData

    private val _forgotPassCustomSmsResult = SingleLiveData<ForgotPasswordAuthResult>()
    val forgotPassCustomSmsResult: LiveData<ForgotPasswordAuthResult>
        get() = _forgotPassCustomSmsResult

    private val _useCustomSmsProvider: SingleLiveData<Boolean> = SingleLiveData()

    private val _elapsedTime: MutableLiveData<String> = MutableLiveData("00:00")
    val elapsedTime: LiveData<String>
        get() = _elapsedTime

    private val _showTimer: MutableLiveData<Boolean> = MutableLiveData(false)
    val showTimer: LiveData<Boolean>
        get() = _showTimer

    private var _doNotShowTimmerIfLimitIsReached: SingleLiveData<Boolean> = SingleLiveData()
    val doNotShowTimmerIfLimitIsReached: LiveData<Boolean>
        get() = _doNotShowTimmerIfLimitIsReached

    private val _verifyDigits = SingleLiveData<String>()
    val verifyDigits: LiveData<String>
        get() = _verifyDigits

    private val _signInCurrentPage: MutableLiveData<Int> = MutableLiveData()
    val signInCurrentPage: LiveData<Int>
        get() = _signInCurrentPage


    private var _lastLoginInfo: MutableLiveData<ResourceV2<LastLoggedIn>> = MutableLiveData()
    val lastLoginInfo: LiveData<ResourceV2<LastLoggedIn>>
        get() = _lastLoginInfo

     var visitorId: String = ""
         private set



    init {
        viewModelScope.launch {
            getVisitorIdMethod()
            getLastLoginInfoMethod(visitorId)
        }
    }

    fun getVisitorId() {
        viewModelScope.launch {
            getVisitorIdMethod()
        }
    }

    suspend fun getVisitorIdMethod() {
        visitorId = visitorIdUseCase.getVisitorId()
    }

    fun getLastLoginInfoAPI(visitorId: String) {
        viewModelScope.launch {
            getLastLoginInfoMethod(visitorId)
        }
    }

    suspend fun getLastLoginInfoMethod(visitorId: String) {
        val result = getLastLoggedInUseCase(visitorId)
        when (result) {
            is ResourceV2.Success -> {
                _lastLoginInfo.value = result
            }

            is ResourceV2.Error -> {
                _lastLoginInfo.value = ResourceV2.Error(message = "", errorType = result.errorType)
            }
        }
    }

    fun setSignInCurrentPage(currentPage: Int?) {
        _signInCurrentPage.value = currentPage
    }

    fun setOnForgotPasswordClicked(authMethod: AuthMethod) {
        _onForgotPasswordClicked.value = authMethod
    }

    fun setAuthMethod(authMethod: AuthMethod) {
        _authMethod.value = authMethod
    }

    fun setShowHidenFaceBookProgressBar(value:Boolean) {
        _showHidenFaceBookProgressBar.value = value
    }

    fun setSignInResultLiveData(value : SignInAuthResult) {
        _signInResultLiveData.value  = value
    }

    fun setPhoneNumber(phoneNumber: String) {
        this.phoneNumber = phoneNumber
    }

     fun setPhoneNumberInForgotPass(phone: String) {
         phoneEmail.value = phone
    }

    fun setEmailForgotInForgotPas(string: String) {
        phoneEmail.value = string
    }

    fun setPassword(userPassword: String) {
        this.userPassword = userPassword
    }

    fun setUserEmail(userEmail: String) {
        this.userEmail = userEmail
    }


    fun setForgotPassResultLiveData(status:ForgotPasswordAuthResult){
        _forgotPassResultLiveData.value = status
    }

    fun getUserProfile() = userRepository.getUserProfile()

    fun fetchUserProfile() {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.getUserProfileFlow()
                    .catch { ex ->
                        withContext(Dispatchers.Main) {
                            ex.printStackTrace()
                            when (ex) {
                                is UserNotExistException -> {
                                    _userNotFound.value = true
                                }
                                is UserDeletedExistException -> {
                                    duaAccount.deleteAllData()
                                }
                                is ServerErrorException -> {
                                    setSignInResultLiveData(SignInAuthResult.Error(ServerErrorException("ServerError")))
                                    LoginManager.getInstance().logOut()
                                    AWSInteractor.logOut()
                                    ToastUtil.toast(R.string.an_error_occurred)
                                }
                            }
                            logError(ErrorStatus.SIGN_IN_FETCH_USER_PROFILE)
                        }
                    }
                    .collect { userModelResponse ->
                        when (userModelResponse) {
                            is Result.Success -> {
                                withContext(Dispatchers.Main) {
                                    val user = userModelResponse.data
                                    DuaApplication.instance.sendTokenToServerAndSave()
                                    Timber.tag("signInCallback").e(user.language)

                                    _userNotFound.value = false
                                    duaAccount.getNotification()
                                //    AppsFlyerLib.getInstance().setCustomerUserId(user.cognitoUserId)
                                    linkCleverTapAppsFlyer()
                                    AppLovinSdk.getInstance(DuaApplication.instance.applicationContext).userIdentifier = user.cognitoUserId
                                    sendDataToAppsflyer()
                                    setUserId(user.cognitoUserId)
                                    UXCam.setUserIdentity(user.cognitoUserId)
                                    duaSharedPrefs.setUserGender(user.gender)
                                }
                            }
                            else -> {}
                        }
                    }
        }
    }

    fun clearEmail() {
        userEmail = ""
        userPassword = ""
        _signInResultLiveData.value = null
    }

    fun clearPhone() {
        phoneNumber = ""
        userPassword = ""
        selectedCountryCodeAsInt = null
        _signInResultLiveData.value = null
    }

    fun clearSignInData() {
        userEmail = ""
        phoneNumber = ""
        userPassword = ""
        selectedCountryCodeAsInt = null
        _signInResultLiveData.value = null
    }

    override fun onConnectWithFacebookClicked(facebookLoginModel: AuthModel): LiveData<Result<AuthProviderResponse>> {
        return userRepository.loginWithFacebook(facebookLoginModel)
    }

    override fun onConnectWithGoogleClicked(authModel: AuthModel): LiveData<Result<Boolean>> {
        return userRepository.loginWithGoogle(authModel)
    }

    fun signUpWithSpotted(signUpWithSpottedBody: SignUpWithSpottedBody): LiveData<Result<SignUpWithSpottedRaw>> {

        duaSharedPrefs.setSignUpWithSpottedToken(null)
        return userRepository.signUpWithSpotted(signUpWithSpottedBody)
    }

    override fun loginUser(loginModel: LoginModel) {
        _signInResultLiveData.postValue(SignInAuthResult.Loading())
        AWSInteractor.logIn(loginModel, object : LogInCallBack {
            override fun onResult(signInResult: SignInResult) {
                Timber.tag(TAG).d("Login success: ${signInResult.signInState}")
                _signInResultLiveData.postValue(SignInAuthResult.Success(signInResult))
                duaSharedPrefs.setHasEverSignedIn(true)
            }

            override fun onError(e: Exception) {
                Timber.tag(TAG).d("Login error: ${e.stackTrace}")
                _signInResultLiveData.postValue(SignInAuthResult.Error(e))
            }
        })
    }

    override fun onEmailClicked() {
        _authMethod.value = AuthMethod.EMAIL
    }

    override fun onPhoneClicked() {
        _authMethod.value = AuthMethod.PHONE
    }

    fun onCustomSmsVerificationUsed(state: Boolean) {
        _useCustomSmsProvider.value = state
    }

    private var timer: CountDownTimer? = null
    fun setTimer() {
        if (_showTimer.value == true)
            return
        else {
            createTimer()
            _showTimer.value = true
        }
    }
    private fun createTimer() {
        val triggerTime = System.currentTimeMillis() + 60 * 1000
        timer = object : CountDownTimer(triggerTime - System.currentTimeMillis(), 1000) {
            override fun onTick(p0: Long) {
                var millisUntilFinished = p0
                val minutes = TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished)
                millisUntilFinished -= TimeUnit.MINUTES.toMillis(minutes)

                val seconds = TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished)

                val string = String.format("%02d:%02d", minutes, seconds)
                _elapsedTime.value = string

                if (millisUntilFinished <= 0) {
                    resetTimer()
                }
            }

            override fun onFinish() {
                resetTimer()
            }


        }
        timer?.start()
    }

    private fun resetTimer() {
        timer?.cancel()
        _elapsedTime.value = "00:00"
        _showTimer.value = false
    }

     fun stopTimer(){
        timer?.cancel()
            timer = null
        }

    private fun clearDigits() {
        _verifyDigits.postValue("")
    }

    fun forgotPassword(reCaptchaToken:String) {
        setTimer()
        _forgotPassResultLiveData.postValue(Loading(false))
        val forgotPasswordModel = ForgotPasswordModel(authMethod.value ?: AuthMethod.EMAIL,
            phoneEmail.value?.lowercase() ?: "",reCaptchaToken)

        AWSInteractor.forgotPassword(visitorId, forgotPasswordModel, object : ForgotPasswordCallBack {
            override fun onResult(forgotResult: ForgotPasswordResult) {
                when (forgotResult.state) {
                    ForgotPasswordState.CONFIRMATION_CODE -> {
                        _forgotPassResultLiveData.postValue(ForgotPasswordAuthResult.Success(forgotResult))
                        ToastUtil.toast("Confirmation code is sent to reset password")
                    }
                    else -> {
                        Timber.tag(TAG).e("un-supported forgot password state")
                    }

                }
            }

            override fun onError(e: Exception) {
                when (e) {
                    is UserLambdaValidationException -> {
                        when {
                            e.message?.contains("custom_sms_verification") == true -> {
                                _forgotPassResultLiveData.postValue(ForgotPasswordAuthResult.CustomSmsVerification)
                            }

                            e.errorMessage.contains("Limit reached") ->{
                                _doNotShowTimmerIfLimitIsReached.postValue(true)
                                _forgotPassResultLiveData.postValue(ForgotPasswordAuthResult.Error(e))
                            }
                            else -> _forgotPassResultLiveData.postValue(ForgotPasswordAuthResult.Error(e))
                        }
                    }

                    is LimitExceededException -> {
                        _doNotShowTimmerIfLimitIsReached.postValue(true)
                        _forgotPassResultLiveData.postValue(ForgotPasswordAuthResult.Error(e))
                    }

                    else -> _forgotPassResultLiveData.postValue(ForgotPasswordAuthResult.Error(e))

                }
                Timber.tag(TAG).e(e, "forgot password error");
            }
        })
    }


    fun verifyForgotPassword(code: String) {
        if (_useCustomSmsProvider.value == true) {
            verifyForgotPasswordWithCustomSms(code)
        } else {
            _forgotPassResultLiveData.postValue(Loading())
            val newPassword = userPassword
            val verifyAccountModel = ConfirmForgotPasswordModel(newPassword,code)
            if (verifyAccountModel != null) {
                AWSInteractor.confirmForgotPassword(verifyAccountModel, object : ForgotPasswordCallBack {
                    override fun onResult(forgotResult: ForgotPasswordResult) {
                        when (forgotResult.state) {
                            ForgotPasswordState.DONE -> {
                                val email = if(authMethod.value == AuthMethod.EMAIL) phoneEmail.value?.lowercase() else null
                                val phone = if(authMethod.value == AuthMethod.PHONE) phoneEmail.value else null
                                val credentials = Credentials(email = email, phone = phone, password = userPassword)
                                sendCredentialsBeforeLoginIn(credentials)
                                ToastUtil.toast("Logging in, please wait..")
                                loginUser(
                                    LoginModel(
                                        phoneEmail.value?.lowercase() ?: "",
                                        userPassword,
                                        authMethod.value!!
                                    )
                                )
                            }
                            else -> Timber.tag(TAG)
                                .e("un-supported forgot password state")
                        }
                        _forgotPassResultLiveData.postValue(ForgotPasswordAuthResult.Success(forgotResult))
                    }

                                override fun onError(e: Exception) {
                        when (e) {
                            is CodeMismatchException -> {
                                _forgotPassResultLiveData.postValue(ForgotPasswordAuthResult.Error(e))
                            }
                            else -> _forgotPassResultLiveData.postValue(ForgotPasswordAuthResult.Error(e))
                        }
                        Timber.tag(TAG).e(e, "forgot password error")
//                        clearDigits()
                    }
                })
            }

        }
    }

    private fun verifyForgotPasswordWithCustomSms(code: String) {
        _forgotPassCustomSmsResult.postValue(ForgotPasswordAuthResult.Loading())
        viewModelScope.launch(Dispatchers.IO) {
            val password = userPassword
            val phone = phoneEmail.value ?: ""
            val model = VerifyCustomSmsModel(phone, code, password)

            userRepository.confirmForgotCustomSms(model)
                .catch { exception ->
                    if (exception is Exception)
                        _forgotPassCustomSmsResult.postValue(ForgotPasswordAuthResult.Error(exception))
//                    clearDigits()
                    exception.printStackTrace()
                }.collect { value ->
                    when (value) {
                        is Resource.Success -> {
                            val credentials = Credentials(phone = phone, password = userPassword)
                            sendCredentialsBeforeLoginIn(credentials)

                            ToastUtil.toast("Logging in, please wait..")
                            loginUser(LoginModel(phoneEmail.value?.lowercase() ?: "", userPassword
                                ?: "", authMethod.value!!))
                        }
                        is Resource.Loading -> {
                        }
                        else -> {}
                    }
                }
        }
    }

    fun sendDataToAppsflyer(){
        appsFlyerBackendManager.sendData()
    }


    fun sendCredentialsBeforeLoginIn(credentials: Credentials) {
        viewModelScope.launch(Dispatchers.IO) {
            userRepository.sendEmailCredentials(credentials)
                .catch { e ->
                    if (e is Exception)
                        e.printStackTrace()
                }
                .collect { value ->
                    when (value) {
                        is Resource.Success -> {
                            // handle success case
                        }
                        is Resource.Loading -> {
                            // handle loading case
                        }
                        is Resource.Error -> {
                            // handle error case
                        }
                    }
                }
        }
    }
}